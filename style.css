/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #9333EA 100%);
    min-height: 100vh;
    padding: 25px;
}

/* Header styles */
.header {
    background: white;
    border-radius: 10px;
    padding: 15px 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    background: #458ff6;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.logo-text {
    font-size: 20px;
    font-weight: bold;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 25px;
}

.nav-link {
    text-decoration: none;
    color: #233348;
    padding: 10px 18px;
    border-radius: 8px;
    transition: all 0.3s;
    font-weight: 500;
}

.nav-link:hover {
    background: #F3F4F6;
    color: #458ff6;
}

.nav-link:first-child {
    background: #458ff6;
    color: white;
}

.nav-link:first-child:hover {
    background: #3a7bd5;
    color: white;
}

/* Hero section */
.hero {
    background: white;
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    max-width: 1200px;
    margin: 0 auto;
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
    min-height: 400px;
}

.hero-text {
    flex: 1;
    display: flex;
    align-items: center;
}

.text-box {
    background: white;
    border: 4px solid #10B981;
    border-radius: 12px;
    padding: 35px;
    max-width: 380px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.hero-title {
    font-size: 32px;
    color: #233348;
    margin-bottom: 15px;
    line-height: 1.2;
    font-weight: bold;
}

.hero-description {
    color: #7d7987;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 25px;
}

.btn-primary {
    background: #458ff6;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #3a7bd5;
}

.hero-image {
    flex: 1;
    background: linear-gradient(135deg, #FEF3C7, #FDE68A, #F59E0B);
    border: 4px solid #F59E0B;
    border-radius: 20px;
    padding: 50px;
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.illustration {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.illustration div {
    position: absolute;
    font-size: 45px;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
}

.heart {
    top: 25px;
    left: 40px;
    font-size: 55px;
    color: #EF4444;
}

.medical-chart {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 90px;
    color: #3B82F6;
}

.doctor-male {
    bottom: 40px;
    left: 30px;
    font-size: 50px;
}

.doctor-female {
    bottom: 40px;
    right: 30px;
    font-size: 50px;
}

.shield {
    top: 40px;
    right: 40px;
    font-size: 50px;
    color: #3B82F6;
}

.plus {
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 40px;
    color: #10B981;
    background: white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.laptop {
    bottom: 55%;
    right: 15px;
    font-size: 40px;
    color: #6366F1;
}

