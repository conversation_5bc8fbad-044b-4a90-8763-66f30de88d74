/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* Header styles */
.header {
    background: white;
    border-radius: 10px;
    padding: 15px 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    background: #458ff6;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.logo-text {
    font-size: 20px;
    font-weight: bold;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #233348;
    padding: 8px 15px;
    border-radius: 5px;
    transition: background 0.3s;
}

.nav-link:hover {
    background: #f0f0f0;
}

/* Hero section */
.hero {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.hero-text {
    flex: 1;
    display: flex;
    align-items: center;
}

.text-box {
    background: white;
    border: 3px solid #2ecc71;
    border-radius: 10px;
    padding: 30px;
    max-width: 350px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.hero-title {
    font-size: 32px;
    color: #233348;
    margin-bottom: 15px;
    line-height: 1.2;
    font-weight: bold;
}

.hero-description {
    color: #7d7987;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 25px;
}

.btn-primary {
    background: #458ff6;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #3a7bd5;
}

.hero-image {
    flex: 1;
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    border: 3px solid #fdcb6e;
    border-radius: 15px;
    padding: 40px;
    min-height: 350px;
    position: relative;
    overflow: hidden;
}

.illustration {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.illustration div {
    position: absolute;
    font-size: 40px;
}

.heart {
    top: 20px;
    left: 30px;
    font-size: 50px;
}

.medical-chart {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 80px;
}

.doctor-male {
    bottom: 30px;
    left: 20px;
    font-size: 45px;
}

.doctor-female {
    bottom: 30px;
    right: 20px;
    font-size: 45px;
}

.shield {
    top: 30px;
    right: 30px;
    font-size: 45px;
}

.plus {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 35px;
    color: #2ecc71;
}

.laptop {
    bottom: 50%;
    right: 10px;
    font-size: 35px;
}

/* Services section */
.services {
    background: white;
    border-radius: 15px;
    padding: 40px;
    margin-top: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.services-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 28px;
    color: #233348;
    margin-bottom: 15px;
}

.title-underline {
    width: 60px;
    height: 3px;
    background: #458ff6;
    margin: 0 auto 20px;
    border-radius: 2px;
}

.section-description {
    color: #7d7987;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: box-shadow 0.3s;
}

.service-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.service-icon {
    font-size: 40px;
    margin-bottom: 20px;
}

.service-title {
    font-size: 18px;
    color: #233348;
    margin-bottom: 15px;
}

.service-description {
    color: #7d7987;
    font-size: 14px;
    line-height: 1.6;
}