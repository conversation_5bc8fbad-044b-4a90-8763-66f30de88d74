* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: white;
    padding: 20px;
    min-height: 100vh;
}

/* Header */
.header {
    background: transparent;
    padding: 20px 0;
    margin-bottom: 40px;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    background: #3B82F6;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    text-align: center;
    line-height: 35px;
    margin-right: 12px;
    font-weight: bold;
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
    color: #1F2937;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #6B7280;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
}

.nav-link:first-child {
    background: #3B82F6;
    color: white;
}

.nav-link:hover {
    color: #3B82F6;
}

/* Hero */
.hero {
    background: transparent;
    padding: 40px 0;
}

.hero-content {
    display: flex;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text {
    flex: 1;
}

.text-box {
    background: white;
    padding: 40px;
    max-width: 450px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.hero-title {
    font-size: 48px;
    margin-bottom: 25px;
    line-height: 1.1;
    color: #1F2937;
    font-weight: bold;
}

.hero-description {
    color: #6B7280;
    margin-bottom: 30px;
    font-size: 18px;
    line-height: 1.6;
}

.btn-primary {
    background: #6366F1;
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
}

.hero-image {
    flex: 1;
}

.healthcare-img {
    width: 100%;
    height: auto;
    max-width: 600px;
}

