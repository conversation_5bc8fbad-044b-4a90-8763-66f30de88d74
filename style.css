{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
dy {
  font-family: Arial, sans-serif;
  background: #f8f9fa;
 Header */
eader {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
avbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
ogo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
ogo-icon {
  background: #458FF6;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
ogo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #233348;
av-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
av-link {
  text-decoration: none;
  color: #1F1534;
  font-weight: 400;
  padding: 0.5rem 0;
av-link:hover {
  color: #458FF6;
 Hero Section */
ero {
  background: white;
  padding: 4rem 2rem;
  min-height: 600px;
ero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
ero-text {
  max-width: 450px;
ero-title {
  font-size: 3rem;
  color: #233348;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  font-weight: bold;
  border: 3px solid #458FF6;
  border-width: 0 0 0 5px;
  padding: 0 0 0 20px;
ero-description {
  color: #7D7987;
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  line-height: 1.6;
  padding: 15px 0;
  border: 1px solid #e0e0e0;
  border-width: 1px 0;
  padding: 20px 0;
tn-primary {
  background: #458FF6;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 8px 20px rgba(69, 143, 246, 0.3);
  border-width: 0;
tn-primary:hover {
  box-shadow: 0 12px 25px rgba(69, 143, 246, 0.4);
  transform: translateY(-2px);
ero-image {
  text-align: center;
  font-size: 5rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
 Services Section */
ervices {
  background: white;
  padding: 5rem 2rem;
  margin-top: 3rem;
ervices-header {
  text-align: center;
  max-width: 600px;
  margin: 0 auto 4rem;
  padding: 0 20px;
ection-title {
  font-size: 2.5rem;
  color: #233348;
  margin-bottom: 1rem;
  font-weight: bold;
  padding: 15px 0;
  border: 2px solid #458FF6;
  border-width: 0 0 3px 0;
  display: inline-block;
itle-underline {
  width: 60px;
  height: 4px;
  background: #458FF6;
  margin: 0 auto 2rem;
  border-radius: 2px;
ection-description {
  color: #7D7987;
  font-size: 1.1rem;
  line-height: 1.8;
  padding: 20px 0;
ervices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
ervice-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  border-width: 1px;
ervice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  border: 2px solid #458FF6;
ervice-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  padding: 10px 0;
ervice-title {
  font-size: 1.4rem;
  color: #233348;
  margin-bottom: 1rem;
  font-weight: 600;
  padding: 10px 0;
ervice-description {
  color: #7D7987;
  line-height: 1.6;
  font-size: 1rem;
  padding: 10px 0;
 Responsive */
edia (max-width: 768px) {
  .hero-content {
      grid-template-columns: 1fr;
      text-align: center;
  }
  .hero-title {
      font-size: 2.2rem;
  }
  .nav-menu {
      display: none;
  }
.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    background: #458FF6;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #1F1534;
    font-weight: 400;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: #458FF6;
}

/* Hero Section */
.hero {
    background: white;
    padding: 4rem 2rem;
    min-height: 600px;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    max-width: 450px;
}

.hero-title {
    font-size: 3rem;
    color: #233348;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    font-weight: bold;
    border: 3px solid #458FF6;
    border-width: 0 0 0 5px;
    padding: 0 0 0 20px;
}

.hero-description {
    color: #7D7987;
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    padding: 15px 0;
    border: 1px solid #e0e0e0;
    border-width: 1px 0;
    padding: 20px 0;
}

.btn-primary {
    background: #458FF6;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 8px 20px rgba(69, 143, 246, 0.3);
    border-width: 0;
}

.btn-primary:hover {
    box-shadow: 0 12px 25px rgba(69, 143, 246, 0.4);
    transform: translateY(-2px);
}

.hero-image {
    text-align: center;
    font-size: 5rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Services Section */
.services {
    background: white;
    padding: 5rem 2rem;
    margin-top: 3rem;
}

.services-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 4rem;
    padding: 0 20px;
}

.section-title {
    font-size: 2.5rem;
    color: #233348;
    margin-bottom: 1rem;
    font-weight: bold;
    padding: 15px 0;
    border: 2px solid #458FF6;
    border-width: 0 0 3px 0;
    display: inline-block;
}

.title-underline {
    width: 60px;
    height: 4px;
    background: #458FF6;
    margin: 0 auto 2rem;
    border-radius: 2px;
}

.section-description {
    color: #7D7987;
    font-size: 1.1rem;
    line-height: 1.8;
    padding: 20px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-width: 1px;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border: 2px solid #458FF6;
}

.service-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    padding: 10px 0;
}

.service-title {
    font-size: 1.4rem;
    color: #233348;
    margin-bottom: 1rem;
    font-weight: 600;
    padding: 10px 0;
}

.service-description {
    color: #7D7987;
    line-height: 1.6;
    font-size: 1rem;
    padding: 10px 0;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .nav-menu {
        display: none;
    }
}