* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: white;
    padding: 20px;
}

/* Header */
.header {
    background: white;
    border-radius: 10px;
    padding: 15px 30px;
    margin-bottom: 20px;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    background: blue;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 20px;
}

.nav-link {
    text-decoration: none;
    color: black;
    padding: 8px 15px;
    border-radius: 5px;
}

.nav-link:hover {
    background: lightgray;
}

/* Hero */
.hero {
    background: white;
    border-radius: 15px;
    padding: 40px;
}

.hero-content {
    display: flex;
    gap: 40px;
}

.hero-text {
    flex: 1;
}

.text-box {
    border: 3px solid green;
    border-radius: 10px;
    padding: 30px;
    max-width: 350px;
}

.hero-title {
    font-size: 30px;
    margin-bottom: 15px;
}

.hero-description {
    color: gray;
    margin-bottom: 20px;
}

.btn-primary {
    background: blue;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
}

.hero-image {
    flex: 1;
    border-radius: 15px;
    overflow: hidden;
}

.healthcare-img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

