* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: purple;
    padding: 20px;
}

/* Header */
.header {
    background: white;
    border-radius: 10px;
    padding: 15px 30px;
    margin-bottom: 20px;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    background: blue;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 20px;
}

.nav-link {
    text-decoration: none;
    color: black;
    padding: 8px 15px;
    border-radius: 5px;
}

.nav-link:hover {
    background: lightgray;
}

/* Hero */
.hero {
    background: white;
    border-radius: 15px;
    padding: 40px;
}

.hero-content {
    display: flex;
    gap: 40px;
}

.hero-text {
    flex: 1;
}

.text-box {
    border: 3px solid green;
    border-radius: 10px;
    padding: 30px;
    max-width: 350px;
}

.hero-title {
    font-size: 30px;
    margin-bottom: 15px;
}

.hero-description {
    color: gray;
    margin-bottom: 20px;
}

.btn-primary {
    background: blue;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
}

.hero-image {
    flex: 1;
    background: orange;
    border: 3px solid orange;
    border-radius: 15px;
    padding: 40px;
    position: relative;
    min-height: 300px;
}

.illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

.illustration div {
    position: absolute;
    font-size: 40px;
}

.heart {
    top: 20px;
    left: 30px;
}

.medical-chart {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 60px;
}

.doctor-male {
    bottom: 20px;
    left: 20px;
}

.doctor-female {
    bottom: 20px;
    right: 20px;
}

.shield {
    top: 20px;
    right: 30px;
}

.plus {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
}

.laptop {
    bottom: 50%;
    right: 10px;
}

